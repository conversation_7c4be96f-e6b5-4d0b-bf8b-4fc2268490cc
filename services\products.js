import { 
  addDocument, 
  getDocuments, 
  updateDocument, 
  deleteDocument, 
  subscribeToCollection,
  getDocument 
} from './firestore';
import { getCurrentUser } from './auth';

const PRODUCTS_COLLECTION = 'products';
const INVENTORY_COLLECTION = 'inventory';

// Product Management Functions

export const addProduct = async (productData) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const product = {
      ...productData,
      userId: user.uid,
      createdBy: user.email,
      status: 'active',
      lastScanned: null,
      scanCount: 0
    };

    const result = await addDocument(PRODUCTS_COLLECTION, product);
    
    if (result.success) {
      // Also create an inventory entry
      await addDocument(INVENTORY_COLLECTION, {
        productId: result.id,
        barcode: productData.barcode || productData.productId,
        quantity: productData.quantity || 0,
        minStock: productData.minStock || 10,
        maxStock: productData.maxStock || 100,
        location: productData.location || 'Unknown',
        userId: user.uid,
        lastUpdated: new Date()
      });
    }

    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getProducts = async (filters = {}) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const conditions = [
      { type: 'where', field: 'userId', operator: '==', value: user.uid },
      { type: 'where', field: 'status', operator: '==', value: 'active' },
      { type: 'orderBy', field: 'createdAt', direction: 'desc' }
    ];

    // Add additional filters if provided
    if (filters.category) {
      conditions.push({ 
        type: 'where', 
        field: 'category', 
        operator: '==', 
        value: filters.category 
      });
    }

    if (filters.limit) {
      conditions.push({ type: 'limit', value: filters.limit });
    }

    return await getDocuments(PRODUCTS_COLLECTION, conditions);
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const searchProducts = async (searchTerm) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Get all products for the user (Firestore doesn't support full-text search natively)
    const result = await getProducts();
    
    if (result.success) {
      const filteredProducts = result.data.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.barcode?.includes(searchTerm) ||
        product.productId?.includes(searchTerm) ||
        product.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      return { success: true, data: filteredProducts };
    }

    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getProductByBarcode = async (barcode) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const conditions = [
      { type: 'where', field: 'userId', operator: '==', value: user.uid },
      { type: 'where', field: 'barcode', operator: '==', value: barcode },
      { type: 'limit', value: 1 }
    ];

    const result = await getDocuments(PRODUCTS_COLLECTION, conditions);
    
    if (result.success && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }

    return { success: false, error: 'Product not found' };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const updateProduct = async (productId, updateData) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const result = await updateDocument(PRODUCTS_COLLECTION, productId, {
      ...updateData,
      lastModified: new Date(),
      modifiedBy: user.email
    });

    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const deleteProduct = async (productId) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Soft delete - mark as inactive instead of actually deleting
    const result = await updateDocument(PRODUCTS_COLLECTION, productId, {
      status: 'deleted',
      deletedAt: new Date(),
      deletedBy: user.email
    });

    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Inventory Management Functions

export const updateInventory = async (productId, quantityChange, operation = 'set') => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Get current inventory
    const conditions = [
      { type: 'where', field: 'productId', operator: '==', value: productId },
      { type: 'where', field: 'userId', operator: '==', value: user.uid },
      { type: 'limit', value: 1 }
    ];

    const inventoryResult = await getDocuments(INVENTORY_COLLECTION, conditions);
    
    if (inventoryResult.success && inventoryResult.data.length > 0) {
      const inventory = inventoryResult.data[0];
      let newQuantity;

      switch (operation) {
        case 'add':
          newQuantity = inventory.quantity + quantityChange;
          break;
        case 'subtract':
          newQuantity = Math.max(0, inventory.quantity - quantityChange);
          break;
        case 'set':
        default:
          newQuantity = quantityChange;
          break;
      }

      return await updateDocument(INVENTORY_COLLECTION, inventory.id, {
        quantity: newQuantity,
        lastUpdated: new Date(),
        updatedBy: user.email
      });
    }

    return { success: false, error: 'Inventory record not found' };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

export const getInventory = async (productId = null) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const conditions = [
      { type: 'where', field: 'userId', operator: '==', value: user.uid }
    ];

    if (productId) {
      conditions.push({ 
        type: 'where', 
        field: 'productId', 
        operator: '==', 
        value: productId 
      });
    }

    return await getDocuments(INVENTORY_COLLECTION, conditions);
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Real-time subscriptions

export const subscribeToProducts = (callback) => {
  const user = getCurrentUser();
  if (!user) {
    callback([]);
    return () => {};
  }

  const conditions = [
    { type: 'where', field: 'userId', operator: '==', value: user.uid },
    { type: 'where', field: 'status', operator: '==', value: 'active' },
    { type: 'orderBy', field: 'createdAt', direction: 'desc' }
  ];

  return subscribeToCollection(PRODUCTS_COLLECTION, callback, conditions);
};

export const subscribeToInventory = (callback) => {
  const user = getCurrentUser();
  if (!user) {
    callback([]);
    return () => {};
  }

  const conditions = [
    { type: 'where', field: 'userId', operator: '==', value: user.uid },
    { type: 'orderBy', field: 'lastUpdated', direction: 'desc' }
  ];

  return subscribeToCollection(INVENTORY_COLLECTION, callback, conditions);
};

// Scan tracking
export const recordScan = async (productId, barcode) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Update product scan count and last scanned time
    const result = await updateDocument(PRODUCTS_COLLECTION, productId, {
      lastScanned: new Date(),
      scanCount: 1, // This would need to be incremented properly in a real app
      lastScannedBy: user.email
    });

    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
};
