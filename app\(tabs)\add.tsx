import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Package, DollarSign, FileText, Save, Tag, Hash, MapPin } from 'lucide-react-native';
import Animated, { FadeInUp, SlideInRight } from 'react-native-reanimated';
import { addProduct } from '../../services/products';

export default function AddProductScreen() {
  const [productId, setProductId] = useState('');
  const [productName, setProductName] = useState('');
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [quantity, setQuantity] = useState('');
  const [location, setLocation] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSaveProduct = async () => {
    if (!productId || !productName || !price) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const priceValue = parseFloat(price);
    if (isNaN(priceValue) || priceValue < 0) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }

    const quantityValue = quantity ? parseInt(quantity) : 0;
    if (quantity && (isNaN(quantityValue) || quantityValue < 0)) {
      Alert.alert('Error', 'Please enter a valid quantity');
      return;
    }

    setIsLoading(true);

    const productData = {
      productId: productId.trim(),
      barcode: productId.trim(), // Using productId as barcode for now
      name: productName.trim(),
      price: priceValue,
      category: category.trim() || 'General',
      quantity: quantityValue,
      location: location.trim() || 'Unknown',
      minStock: 10, // Default minimum stock
      maxStock: 100 // Default maximum stock
    };

    const result = await addProduct(productData);
    setIsLoading(false);

    if (result.success) {
      Alert.alert('Success', 'Product added successfully!');
      // Clear form
      setProductId('');
      setProductName('');
      setPrice('');
      setCategory('');
      setQuantity('');
      setLocation('');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  return (
    <LinearGradient
      colors={['#1e40af', '#3b82f6']}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Header */}
        <Animated.View 
          entering={FadeInUp.duration(600)}
          style={styles.header}
        >
          <Text style={styles.title}>Add New Product</Text>
          <Text style={styles.subtitle}>Enter product details to add to inventory</Text>
        </Animated.View>

        {/* Form */}
        <Animated.View 
          entering={FadeInUp.duration(600).delay(200)}
          style={styles.formContainer}
        >
          <View style={styles.card}>
            {/* Product ID Input */}
            <Animated.View 
              entering={SlideInRight.duration(600).delay(300)}
              style={styles.inputContainer}
            >
              <Text style={styles.inputLabel}>Product ID / Barcode</Text>
              <View style={styles.inputWrapper}>
                <Package size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter product ID or scan barcode"
                  placeholderTextColor="#9ca3af"
                  value={productId}
                  onChangeText={setProductId}
                />
              </View>
            </Animated.View>

            {/* Product Name Input */}
            <Animated.View 
              entering={SlideInRight.duration(600).delay(400)}
              style={styles.inputContainer}
            >
              <Text style={styles.inputLabel}>Product Name</Text>
              <View style={styles.inputWrapper}>
                <FileText size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter product name"
                  placeholderTextColor="#9ca3af"
                  value={productName}
                  onChangeText={setProductName}
                />
              </View>
            </Animated.View>

            {/* Price Input */}
            <Animated.View
              entering={SlideInRight.duration(600).delay(500)}
              style={styles.inputContainer}
            >
              <Text style={styles.inputLabel}>Price *</Text>
              <View style={styles.inputWrapper}>
                <DollarSign size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="0.00"
                  placeholderTextColor="#9ca3af"
                  value={price}
                  onChangeText={setPrice}
                  keyboardType="decimal-pad"
                />
              </View>
            </Animated.View>

            {/* Category Input */}
            <Animated.View
              entering={SlideInRight.duration(600).delay(600)}
              style={styles.inputContainer}
            >
              <Text style={styles.inputLabel}>Category</Text>
              <View style={styles.inputWrapper}>
                <Tag size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="e.g., Dairy, Beverages, Snacks"
                  placeholderTextColor="#9ca3af"
                  value={category}
                  onChangeText={setCategory}
                />
              </View>
            </Animated.View>

            {/* Quantity Input */}
            <Animated.View
              entering={SlideInRight.duration(600).delay(700)}
              style={styles.inputContainer}
            >
              <Text style={styles.inputLabel}>Initial Quantity</Text>
              <View style={styles.inputWrapper}>
                <Hash size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="0"
                  placeholderTextColor="#9ca3af"
                  value={quantity}
                  onChangeText={setQuantity}
                  keyboardType="numeric"
                />
              </View>
            </Animated.View>

            {/* Location Input */}
            <Animated.View
              entering={SlideInRight.duration(600).delay(800)}
              style={styles.inputContainer}
            >
              <Text style={styles.inputLabel}>Location</Text>
              <View style={styles.inputWrapper}>
                <MapPin size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="e.g., Aisle 1, Shelf A"
                  placeholderTextColor="#9ca3af"
                  value={location}
                  onChangeText={setLocation}
                />
              </View>
            </Animated.View>

            {/* Save Button */}
            <Animated.View
              entering={FadeInUp.duration(600).delay(900)}
              style={styles.buttonContainer}
            >
              <TouchableOpacity
                style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
                onPress={handleSaveProduct}
                disabled={isLoading}
              >
                <LinearGradient
                  colors={['#f97316', '#ea580c']}
                  style={styles.saveButtonGradient}
                >
                  <Save size={20} color="#ffffff" />
                  <Text style={styles.saveButtonText}>
                    {isLoading ? 'Saving...' : 'Save Product'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          </View>
        </Animated.View>

        {/* Quick Tips */}
        <Animated.View 
          entering={FadeInUp.duration(600).delay(700)}
          style={styles.tipsContainer}
        >
          <Text style={styles.tipsTitle}>Quick Tips:</Text>
          <Text style={styles.tipText}>• Scan barcode to auto-fill Product ID</Text>
          <Text style={styles.tipText}>• Use descriptive names for easy searching</Text>
          <Text style={styles.tipText}>• Double-check prices before saving</Text>
        </Animated.View>
      </ScrollView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 30,
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 24,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 15,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    paddingHorizontal: 16,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1f2937',
  },
  buttonContainer: {
    marginTop: 8,
  },
  saveButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
  },
  saveButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    marginLeft: 8,
  },
  tipsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  tipsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 6,
    lineHeight: 20,
  },
});