import { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Dimensions, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { ShoppingCart, Lock, User, Eye, EyeOff, Mail, UserPlus } from 'lucide-react-native';
import Animated, { FadeInUp, FadeInDown, SlideInRight } from 'react-native-reanimated';
import { signIn, signUp, onAuthStateChange } from '../services/auth';

const { width, height } = Dimensions.get('window');

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);

  useEffect(() => {
    // Check if user is already authenticated
    const unsubscribe = onAuthStateChange((user) => {
      if (user) {
        router.replace('/(tabs)/scan');
      }
    });

    return () => unsubscribe();
  }, []);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setIsLoading(true);

    const result = await signIn(email, password);
    setIsLoading(false);

    if (result.success) {
      router.replace('/(tabs)/scan');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const handleSignUp = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    setIsLoading(true);

    const result = await signUp(email, password);
    setIsLoading(false);

    if (result.success) {
      Alert.alert('Success', 'Account created successfully!');
      router.replace('/(tabs)/scan');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={['#1e40af', '#3b82f6', '#7c3aed']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.content}>
          {/* Header */}
          <Animated.View 
            entering={FadeInUp.duration(800)}
            style={styles.header}
          >
            <View style={styles.logoContainer}>
              <ShoppingCart size={width * 0.12} color="#ffffff" strokeWidth={2} />
            </View>
            <Text style={styles.title}>QuickTag</Text>
            <Text style={styles.subtitle}>Supermarket Barcode Manager</Text>
          </Animated.View>

          {/* Login Form */}
          <Animated.View 
            entering={FadeInUp.duration(800).delay(200)}
            style={styles.formContainer}
          >
            <View style={styles.card}>
              <Text style={styles.cardTitle}>
                {isSignUp ? 'Create Account' : 'Welcome Back'}
              </Text>
              <Text style={styles.cardSubtitle}>
                {isSignUp ? 'Sign up to start managing inventory' : 'Sign in to manage your inventory'}
              </Text>

              {/* Email Input */}
              <Animated.View
                entering={SlideInRight.duration(600).delay(400)}
                style={styles.inputContainer}
              >
                <View style={styles.inputWrapper}>
                  <Mail size={20} color="#6b7280" style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="Email"
                    placeholderTextColor="#9ca3af"
                    value={email}
                    onChangeText={setEmail}
                    autoCapitalize="none"
                    keyboardType="email-address"
                  />
                </View>
              </Animated.View>

              {/* Password Input */}
              <Animated.View 
                entering={SlideInRight.duration(600).delay(500)}
                style={styles.inputContainer}
              >
                <View style={styles.inputWrapper}>
                  <Lock size={20} color="#6b7280" style={styles.inputIcon} />
                  <TextInput
                    style={[styles.input, { paddingRight: 50 }]}
                    placeholder="Password"
                    placeholderTextColor="#9ca3af"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                  />
                  <TouchableOpacity
                    style={styles.eyeIcon}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff size={20} color="#6b7280" />
                    ) : (
                      <Eye size={20} color="#6b7280" />
                    )}
                  </TouchableOpacity>
                </View>
              </Animated.View>

              {/* Auth Button */}
              <Animated.View entering={FadeInUp.duration(600).delay(600)}>
                <TouchableOpacity
                  style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
                  onPress={isSignUp ? handleSignUp : handleLogin}
                  disabled={isLoading}
                >
                  <LinearGradient
                    colors={['#f97316', '#ea580c']}
                    style={styles.loginButtonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    {isSignUp ? (
                      <UserPlus size={20} color="#ffffff" style={{ marginRight: 8 }} />
                    ) : (
                      <User size={20} color="#ffffff" style={{ marginRight: 8 }} />
                    )}
                    <Text style={styles.loginButtonText}>
                      {isLoading
                        ? (isSignUp ? 'Creating Account...' : 'Signing In...')
                        : (isSignUp ? 'Create Account' : 'Sign In')
                      }
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>

              {/* Toggle Sign Up/Sign In */}
              <Animated.View
                entering={FadeInDown.duration(600).delay(800)}
                style={styles.toggleContainer}
              >
                <Text style={styles.toggleText}>
                  {isSignUp ? 'Already have an account?' : "Don't have an account?"}
                </Text>
                <TouchableOpacity onPress={() => setIsSignUp(!isSignUp)}>
                  <Text style={styles.toggleButton}>
                    {isSignUp ? 'Sign In' : 'Sign Up'}
                  </Text>
                </TouchableOpacity>
              </Animated.View>
            </View>
          </Animated.View>
        </View>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.05,
    paddingTop: height * 0.08,
    minHeight: height,
  },
  header: {
    alignItems: 'center',
    marginBottom: height * 0.06,
  },
  logoContainer: {
    width: width * 0.25,
    height: width * 0.25,
    borderRadius: width * 0.125,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  title: {
    fontSize: Math.min(width * 0.1, 42),
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: -1,
  },
  subtitle: {
    fontSize: Math.min(width * 0.04, 16),
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 24,
    padding: width * 0.08,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 20,
  },
  cardTitle: {
    fontSize: Math.min(width * 0.07, 28),
    fontFamily: 'Inter-Bold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: Math.min(width * 0.04, 16),
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    paddingHorizontal: 16,
    height: Math.max(56, height * 0.07),
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: Math.min(width * 0.04, 16),
    fontFamily: 'Inter-Medium',
    color: '#1f2937',
  },
  eyeIcon: {
    position: 'absolute',
    right: 16,
    padding: 4,
  },
  loginButton: {
    marginTop: 12,
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  loginButtonDisabled: {
    opacity: 0.7,
  },
  loginButtonGradient: {
    paddingVertical: Math.max(18, height * 0.025),
    alignItems: 'center',
  },
  loginButtonText: {
    fontSize: Math.min(width * 0.045, 18),
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  toggleText: {
    fontSize: Math.min(width * 0.035, 14),
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginRight: 8,
  },
  toggleButton: {
    fontSize: Math.min(width * 0.035, 14),
    fontFamily: 'Inter-SemiBold',
    color: '#f97316',
  },
});