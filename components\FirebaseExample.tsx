import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { signIn, signUp, logOut, onAuthStateChange, getCurrentUser } from '../services/auth';
import { addDocument, getDocuments } from '../services/firestore';

export default function FirebaseExample() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [testData, setTestData] = useState('');

  useEffect(() => {
    // Listen for authentication state changes
    const unsubscribe = onAuthStateChange((user) => {
      setUser(user);
    });

    return () => unsubscribe();
  }, []);

  const handleSignUp = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    const result = await signUp(email, password);
    setLoading(false);

    if (result.success) {
      Alert.alert('Success', 'Account created successfully!');
      setEmail('');
      setPassword('');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    const result = await signIn(email, password);
    setLoading(false);

    if (result.success) {
      Alert.alert('Success', 'Signed in successfully!');
      setEmail('');
      setPassword('');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const handleSignOut = async () => {
    const result = await logOut();
    if (result.success) {
      Alert.alert('Success', 'Signed out successfully!');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const handleAddData = async () => {
    if (!testData) {
      Alert.alert('Error', 'Please enter some test data');
      return;
    }

    const result = await addDocument('test_collection', {
      data: testData,
      userId: user?.uid || 'anonymous'
    });

    if (result.success) {
      Alert.alert('Success', 'Data added to Firestore!');
      setTestData('');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const handleGetData = async () => {
    const result = await getDocuments('test_collection');
    
    if (result.success) {
      Alert.alert('Data Retrieved', `Found ${result.data.length} documents`);
      console.log('Firestore data:', result.data);
    } else {
      Alert.alert('Error', result.error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Firebase Integration Test</Text>
      
      {user ? (
        <View style={styles.userInfo}>
          <Text style={styles.userText}>Signed in as: {user.email}</Text>
          <TouchableOpacity style={styles.button} onPress={handleSignOut}>
            <Text style={styles.buttonText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.authForm}>
          <TextInput
            style={styles.input}
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          <TextInput
            style={styles.input}
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={[styles.button, styles.buttonHalf]} 
              onPress={handleSignIn}
              disabled={loading}
            >
              <Text style={styles.buttonText}>Sign In</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.button, styles.buttonHalf]} 
              onPress={handleSignUp}
              disabled={loading}
            >
              <Text style={styles.buttonText}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={styles.firestoreSection}>
        <Text style={styles.sectionTitle}>Firestore Test</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter test data"
          value={testData}
          onChangeText={setTestData}
        />
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.buttonHalf]} 
            onPress={handleAddData}
          >
            <Text style={styles.buttonText}>Add Data</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.button, styles.buttonHalf]} 
            onPress={handleGetData}
          >
            <Text style={styles.buttonText}>Get Data</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  userInfo: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  userText: {
    fontSize: 16,
    marginBottom: 10,
    color: '#333',
  },
  authForm: {
    marginBottom: 30,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonHalf: {
    flex: 0.48,
  },
  firestoreSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
});
