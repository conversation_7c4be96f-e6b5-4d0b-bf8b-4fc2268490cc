import { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, FlatList, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Search, Filter, Package, DollarSign, Tag, MapPin } from 'lucide-react-native';
import Animated, { FadeInUp, SlideInLeft } from 'react-native-reanimated';
import { getProducts, searchProducts, subscribeToProducts } from '../../services/products';

export default function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    // Subscribe to real-time product updates
    const unsubscribe = subscribeToProducts((productList) => {
      setProducts(productList);
      if (searchQuery.trim() === '') {
        setFilteredProducts(productList);
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);

    if (query.trim() === '') {
      setFilteredProducts(products);
      return;
    }

    setIsSearching(true);
    const result = await searchProducts(query);
    setIsSearching(false);

    if (result.success) {
      setFilteredProducts(result.data);
    } else {
      // Fallback to local filtering if search fails
      const filtered = products.filter(
        product =>
          product.name.toLowerCase().includes(query.toLowerCase()) ||
          product.barcode?.includes(query) ||
          product.productId?.includes(query) ||
          product.category?.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredProducts(filtered);
    }
  };

  const renderProductItem = ({ item, index }: { item: any; index: number }) => (
    <Animated.View
      entering={SlideInLeft.duration(400).delay(index * 100)}
      style={styles.productItem}
    >
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productId}>ID: {item.productId || item.barcode}</Text>
        {item.category && (
          <View style={styles.categoryContainer}>
            <Tag size={12} color="#6b7280" />
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
        )}
        {item.location && (
          <View style={styles.locationContainer}>
            <MapPin size={12} color="#6b7280" />
            <Text style={styles.locationText}>{item.location}</Text>
          </View>
        )}
      </View>
      <View style={styles.productPrice}>
        <Text style={styles.priceText}>${item.price?.toFixed(2) || '0.00'}</Text>
        {item.quantity !== undefined && (
          <Text style={styles.quantityText}>Qty: {item.quantity}</Text>
        )}
      </View>
    </Animated.View>
  );

  return (
    <LinearGradient
      colors={['#1e40af', '#3b82f6']}
      style={styles.container}
    >
      <View style={styles.content}>
        {/* Header */}
        <Animated.View 
          entering={FadeInUp.duration(600)}
          style={styles.header}
        >
          <Text style={styles.title}>Search Products</Text>
          <Text style={styles.subtitle}>Find products by name or ID</Text>
        </Animated.View>

        {/* Search Bar */}
        <Animated.View 
          entering={FadeInUp.duration(600).delay(200)}
          style={styles.searchContainer}
        >
          <View style={styles.searchWrapper}>
            <Search size={20} color="#6b7280" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search by name or product ID..."
              placeholderTextColor="#9ca3af"
              value={searchQuery}
              onChangeText={handleSearch}
            />
          </View>
          <TouchableOpacity style={styles.filterButton}>
            <Filter size={20} color="#ffffff" />
          </TouchableOpacity>
        </Animated.View>

        {/* Results Header */}
        <Animated.View
          entering={FadeInUp.duration(600).delay(300)}
          style={styles.resultsHeader}
        >
          <Text style={styles.resultsText}>
            {isLoading ? 'Loading...' :
             isSearching ? 'Searching...' :
             `${filteredProducts.length} product${filteredProducts.length !== 1 ? 's' : ''} found`
            }
          </Text>
        </Animated.View>

        {/* Products List */}
        <View style={styles.listContainer}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#ffffff" />
              <Text style={styles.loadingText}>Loading products...</Text>
            </View>
          ) : (
            <FlatList
              data={filteredProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContent}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Package size={48} color="rgba(255, 255, 255, 0.5)" />
                  <Text style={styles.emptyText}>
                    {searchQuery ? 'No products found' : 'No products added yet'}
                  </Text>
                  <Text style={styles.emptySubtext}>
                    {searchQuery ? 'Try a different search term' : 'Add your first product to get started'}
                  </Text>
                </View>
              }
            />
          )}
        </View>

        {/* Quick Stats */}
        {!isLoading && (
          <Animated.View
            entering={FadeInUp.duration(600).delay(400)}
            style={styles.statsContainer}
          >
            <View style={styles.statCard}>
              <Package size={24} color="#f97316" />
              <Text style={styles.statNumber}>{products.length}</Text>
              <Text style={styles.statLabel}>Total Products</Text>
            </View>
            <View style={styles.statCard}>
              <DollarSign size={24} color="#f97316" />
              <Text style={styles.statNumber}>
                ${products.reduce((sum, product) => sum + (product.price || 0), 0).toFixed(2)}
              </Text>
              <Text style={styles.statLabel}>Total Value</Text>
            </View>
          </Animated.View>
        )}
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  searchWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    paddingHorizontal: 16,
    height: 50,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1f2937',
  },
  filterButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  resultsHeader: {
    marginBottom: 16,
  },
  resultsText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.9)',
  },
  listContainer: {
    flex: 1,
    marginBottom: 20,
  },
  listContent: {
    paddingBottom: 20,
  },
  productItem: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  productId: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  categoryText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    marginLeft: 4,
  },
  productPrice: {
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  priceText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#f97316',
  },
  quantityText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
    marginTop: 2,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 8,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 6,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statNumber: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
});