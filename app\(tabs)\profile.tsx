import { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { User, Settings, Bell, Shield, CircleHelp as HelpCircle, LogOut, Zap, Package } from 'lucide-react-native';
import Animated, { FadeInUp, SlideInRight } from 'react-native-reanimated';
import { logOut, getCurrentUser, onAuthStateChange } from '../../services/auth';
import { getProducts } from '../../services/products';

export default function ProfileScreen() {
  const [user, setUser] = useState(null);
  const [productCount, setProductCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get current user and listen for auth changes
    const unsubscribe = onAuthStateChange((currentUser) => {
      setUser(currentUser);
      if (currentUser) {
        loadUserStats();
      } else {
        router.replace('/');
      }
    });

    return () => unsubscribe();
  }, []);

  const loadUserStats = async () => {
    try {
      const productsResult = await getProducts();
      if (productsResult.success) {
        setProductCount(productsResult.data.length);
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            const result = await logOut();
            if (result.success) {
              router.replace('/');
            } else {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          }
        }
      ]
    );
  };

  const MenuItem = ({ icon: Icon, title, subtitle, onPress, delay = 0 }: any) => (
    <Animated.View entering={SlideInRight.duration(500).delay(delay)}>
      <TouchableOpacity style={styles.menuItem} onPress={onPress}>
        <View style={styles.menuIconContainer}>
          <Icon size={24} color="#f97316" />
        </View>
        <View style={styles.menuContent}>
          <Text style={styles.menuTitle}>{title}</Text>
          {subtitle && <Text style={styles.menuSubtitle}>{subtitle}</Text>}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <LinearGradient
      colors={['#1e40af', '#3b82f6']}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Header */}
        <Animated.View 
          entering={FadeInUp.duration(600)}
          style={styles.header}
        >
          <View style={styles.avatarContainer}>
            <User size={48} color="#ffffff" strokeWidth={2} />
          </View>
          <Text style={styles.userName}>
            {user?.displayName || 'Store Manager'}
          </Text>
          <Text style={styles.userEmail}>
            {user?.email || 'Loading...'}
          </Text>
        </Animated.View>

        {/* Stats Cards */}
        <Animated.View 
          entering={FadeInUp.duration(600).delay(200)}
          style={styles.statsContainer}
        >
          <View style={styles.statCard}>
            <Package size={24} color="#f97316" />
            <Text style={styles.statNumber}>
              {isLoading ? '...' : productCount}
            </Text>
            <Text style={styles.statLabel}>Products Added</Text>
          </View>
          <View style={styles.statCard}>
            <User size={24} color="#f97316" />
            <Text style={styles.statNumber}>
              {user?.metadata?.creationTime ?
                Math.floor((Date.now() - new Date(user.metadata.creationTime).getTime()) / (1000 * 60 * 60 * 24))
                : '...'
              }
            </Text>
            <Text style={styles.statLabel}>Days Active</Text>
          </View>
        </Animated.View>

        {/* Menu */}
        <Animated.View 
          entering={FadeInUp.duration(600).delay(300)}
          style={styles.menuContainer}
        >
          <View style={styles.menuCard}>
            <MenuItem
              icon={Settings}
              title="Settings"
              subtitle="App preferences and configuration"
              onPress={() => Alert.alert('Settings', 'Settings screen coming soon!')}
              delay={400}
            />
            <MenuItem
              icon={Bell}
              title="Notifications"
              subtitle="Manage your notifications"
              onPress={() => Alert.alert('Notifications', 'Notification settings coming soon!')}
              delay={500}
            />
            <MenuItem
              icon={Shield}
              title="Security"
              subtitle="Password and security settings"
              onPress={() => Alert.alert('Security', 'Security settings coming soon!')}
              delay={600}
            />
            <MenuItem
              icon={HelpCircle}
              title="Help & Support"
              subtitle="Get help and contact support"
              onPress={() => Alert.alert('Help', 'Help & Support coming soon!')}
              delay={700}
            />
          </View>
        </Animated.View>

        {/* Logout Button */}
        <Animated.View 
          entering={FadeInUp.duration(600).delay(800)}
          style={styles.logoutContainer}
        >
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <LinearGradient
              colors={['#ef4444', '#dc2626']}
              style={styles.logoutButtonGradient}
            >
              <LogOut size={20} color="#ffffff" />
              <Text style={styles.logoutButtonText}>Logout</Text>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  userName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  statCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statNumber: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  menuContainer: {
    marginBottom: 30,
  },
  menuCard: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 15,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  menuIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fef3e2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1f2937',
    marginBottom: 4,
  },
  menuSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  logoutContainer: {
    marginTop: 20,
  },
  logoutButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  logoutButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  logoutButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    marginLeft: 8,
  },
});