// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCX--RWkCRkpKLghoLMhefK0xpxU-toH9k",
  authDomain: "quicktag-26279.firebaseapp.com",
  projectId: "quicktag-26279",
  storageBucket: "quicktag-26279.firebasestorage.app",
  messagingSenderId: "1062681204152",
  appId: "1:1062681204152:web:33f3864181551ddf0b6b4d",
  measurementId: "G-E3STD1L8T8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

// Initialize Analytics (only for web)
let analytics = null;
if (typeof window !== 'undefined') {
  analytics = getAnalytics(app);
}

export { app, auth, db, storage, analytics };
