import { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Camera, ShoppingCart, Zap, Package, CheckCircle, XCircle } from 'lucide-react-native';
import Animated, { FadeInUp, FadeInDown, SlideInLeft } from 'react-native-reanimated';
import { useResponsiveLayout } from '../../hooks/useResponsive';
import { getProductByBarcode, recordScan, updateInventory } from '../../services/products';

export default function ScanScreen() {
  const [isScanning, setIsScanning] = useState(false);
  const [lastScannedProduct, setLastScannedProduct] = useState(null);

  // Use responsive layout
  const { width, height, isSmallScreen, isTablet, spacing, fontSizes, insets, contentPadding } = useResponsiveLayout();

  const handleScan = async () => {
    setIsScanning(true);

    // Simulate scanning - in a real app, this would use the camera
    // For demo purposes, we'll simulate finding a random product
    setTimeout(async () => {
      // Simulate a barcode scan result
      const simulatedBarcode = '1234567890'; // In real app, this comes from camera

      try {
        const result = await getProductByBarcode(simulatedBarcode);

        if (result.success) {
          const product = result.data;
          setLastScannedProduct(product);

          // Record the scan
          await recordScan(product.id, simulatedBarcode);

          Alert.alert(
            'Product Found!',
            `Name: ${product.name}\nPrice: $${product.price?.toFixed(2) || '0.00'}\nCategory: ${product.category || 'N/A'}\nLocation: ${product.location || 'N/A'}`,
            [
              { text: 'OK', style: 'default' },
              {
                text: 'Update Inventory',
                onPress: () => handleInventoryUpdate(product),
                style: 'default'
              }
            ]
          );
        } else {
          Alert.alert(
            'Product Not Found',
            `Barcode: ${simulatedBarcode}\n\nThis product is not in your inventory. Would you like to add it?`,
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Add Product', onPress: () => {/* Navigate to add screen */} }
            ]
          );
        }
      } catch (error) {
        Alert.alert('Error', 'Failed to scan product. Please try again.');
      }

      setIsScanning(false);
    }, 2000);
  };

  const handleInventoryUpdate = (product) => {
    Alert.alert(
      'Update Inventory',
      `Current quantity: ${product.quantity || 0}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Add 1', onPress: () => updateProductInventory(product.id, 1, 'add') },
        { text: 'Remove 1', onPress: () => updateProductInventory(product.id, 1, 'subtract') },
        { text: 'Set Quantity', onPress: () => promptForQuantity(product.id) }
      ]
    );
  };

  const updateProductInventory = async (productId, quantity, operation) => {
    const result = await updateInventory(productId, quantity, operation);

    if (result.success) {
      Alert.alert('Success', 'Inventory updated successfully!');
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const promptForQuantity = (productId) => {
    Alert.prompt(
      'Set Quantity',
      'Enter the new quantity:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Update',
          onPress: (value) => {
            const quantity = parseInt(value);
            if (!isNaN(quantity) && quantity >= 0) {
              updateProductInventory(productId, quantity, 'set');
            } else {
              Alert.alert('Error', 'Please enter a valid quantity');
            }
          }
        }
      ],
      'plain-text',
      '',
      'numeric'
    );
  };

  return (
    <LinearGradient
      colors={['#1e40af', '#3b82f6']}
      style={styles.container}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.content,
          {
            paddingHorizontal: contentPadding,
            paddingTop: insets.top + spacing.lg,
            paddingBottom: insets.bottom + spacing.lg,
            minHeight: height - insets.top - insets.bottom,
          }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View
          entering={FadeInUp.duration(600)}
          style={[styles.header, { marginBottom: isSmallScreen ? spacing.lg : spacing.xl }]}
        >
          <Text style={[styles.title, { fontSize: isSmallScreen ? fontSizes.xl : fontSizes.xxl }]}>
            Scan Products
          </Text>
          <Text style={[styles.subtitle, { fontSize: fontSizes.md }]}>
            Point camera at barcode or QR code
          </Text>
        </Animated.View>

        {/* Scanner Area */}
        <Animated.View
          entering={FadeInUp.duration(600).delay(200)}
          style={[styles.scannerContainer, { marginBottom: isSmallScreen ? spacing.lg : spacing.xl }]}
        >
          <View style={[
            styles.scannerFrame,
            {
              width: Math.min(width - (contentPadding * 2) - 40, isTablet ? 400 : 300),
              height: Math.min(width - (contentPadding * 2) - 40, isTablet ? 400 : 300),
            }
          ]}>
            <View style={styles.scannerOverlay}>
              {isScanning ? (
                <Animated.View
                  entering={FadeInUp.duration(300)}
                  style={styles.scanningIndicator}
                >
                  <Zap size={isSmallScreen ? width * 0.1 : width * 0.12} color="#f97316" />
                  <Text style={[styles.scanningText, { fontSize: fontSizes.lg }]}>
                    Scanning...
                  </Text>
                </Animated.View>
              ) : (
                <Animated.View
                  entering={FadeInUp.duration(300)}
                  style={styles.scanPrompt}
                >
                  <Camera
                    size={isSmallScreen ? width * 0.14 : width * 0.16}
                    color="#ffffff"
                    strokeWidth={1}
                  />
                  <Text style={[styles.scanPromptText, { fontSize: fontSizes.md }]}>
                    Tap to scan barcode
                  </Text>
                </Animated.View>
              )}
            </View>
            <View style={styles.cornerTopLeft} />
            <View style={styles.cornerTopRight} />
            <View style={styles.cornerBottomLeft} />
            <View style={styles.cornerBottomRight} />
          </View>
        </Animated.View>

        {/* Scan Button */}
        <Animated.View
          entering={FadeInDown.duration(600).delay(300)}
          style={[styles.buttonContainer, { marginBottom: isSmallScreen ? spacing.lg : spacing.xl }]}
        >
          <TouchableOpacity
            style={styles.scanButton}
            onPress={handleScan}
            disabled={isScanning}
          >
            <LinearGradient
              colors={['#f97316', '#ea580c']}
              style={[
                styles.scanButtonGradient,
                {
                  paddingVertical: isSmallScreen ? spacing.md : spacing.lg,
                  paddingHorizontal: spacing.xl,
                }
              ]}
            >
              <Camera size={isSmallScreen ? 20 : 24} color="#ffffff" />
              <Text style={[styles.scanButtonText, { fontSize: fontSizes.lg }]}>
                {isScanning ? 'Scanning...' : 'Start Scan'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* Quick Stats */}
        <Animated.View
          entering={SlideInLeft.duration(600).delay(400)}
          style={styles.statsContainer}
        >
          <View style={[styles.statCard, { padding: isSmallScreen ? spacing.md : spacing.lg }]}>
            <Package size={isSmallScreen ? 20 : 24} color="#f97316" />
            <Text style={[styles.statNumber, { fontSize: isSmallScreen ? fontSizes.lg : fontSizes.xl }]}>
              1,247
            </Text>
            <Text style={[styles.statLabel, { fontSize: fontSizes.xs }]}>
              Products Scanned
            </Text>
          </View>
          <View style={[styles.statCard, { padding: isSmallScreen ? spacing.md : spacing.lg }]}>
            <ShoppingCart size={isSmallScreen ? 20 : 24} color="#f97316" />
            <Text style={[styles.statNumber, { fontSize: isSmallScreen ? fontSizes.lg : fontSizes.xl }]}>
              89
            </Text>
            <Text style={[styles.statLabel, { fontSize: fontSizes.xs }]}>
              Added Today
            </Text>
          </View>
        </Animated.View>
      </ScrollView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  scannerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    position: 'relative',
  },
  scannerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  scanningIndicator: {
    alignItems: 'center',
  },
  scanningText: {
    fontFamily: 'Inter-SemiBold',
    color: '#f97316',
    marginTop: 12,
  },
  scanPrompt: {
    alignItems: 'center',
  },
  scanPromptText: {
    fontFamily: 'Inter-Medium',
    color: '#ffffff',
    marginTop: 16,
    textAlign: 'center',
  },
  cornerTopLeft: {
    position: 'absolute',
    top: -2,
    left: -2,
    width: 30,
    height: 30,
    borderTopWidth: 4,
    borderLeftWidth: 4,
    borderColor: '#f97316',
    borderTopLeftRadius: 20,
  },
  cornerTopRight: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 30,
    height: 30,
    borderTopWidth: 4,
    borderRightWidth: 4,
    borderColor: '#f97316',
    borderTopRightRadius: 20,
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: -2,
    left: -2,
    width: 30,
    height: 30,
    borderBottomWidth: 4,
    borderLeftWidth: 4,
    borderColor: '#f97316',
    borderBottomLeftRadius: 20,
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 30,
    height: 30,
    borderBottomWidth: 4,
    borderRightWidth: 4,
    borderColor: '#f97316',
    borderBottomRightRadius: 20,
  },
  buttonContainer: {
    // marginBottom handled dynamically
  },
  scanButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  scanButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanButtonText: {
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    marginLeft: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statNumber: {
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 8,
  },
  statLabel: {
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
});