import { useState, useEffect } from 'react';
import { Dimensions, PixelRatio } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export interface ResponsiveDimensions {
  width: number;
  height: number;
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
  isTablet: boolean;
  isLandscape: boolean;
  scale: number;
  fontScale: number;
}

export interface ResponsiveSpacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
}

export interface ResponsiveFontSizes {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

/**
 * Hook for responsive dimensions that updates on orientation changes
 */
export const useResponsiveDimensions = (): ResponsiveDimensions => {
  const [dimensions, setDimensions] = useState(Dimensions.get('window'));
  
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
    });
    
    return () => subscription?.remove();
  }, []);

  const { width, height } = dimensions;
  const scale = PixelRatio.get();
  const fontScale = PixelRatio.getFontScale();
  
  // Screen size categories
  const isSmallScreen = width < 375 || height < 667;
  const isMediumScreen = width >= 375 && width < 768;
  const isLargeScreen = width >= 768 && width < 1024;
  const isTablet = width >= 768;
  const isLandscape = width > height;

  return {
    width,
    height,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isTablet,
    isLandscape,
    scale,
    fontScale,
  };
};

/**
 * Hook for responsive spacing values
 */
export const useResponsiveSpacing = (): ResponsiveSpacing => {
  const { width, isSmallScreen, isTablet } = useResponsiveDimensions();
  
  const baseSpacing = isSmallScreen ? 4 : isTablet ? 8 : 6;
  
  return {
    xs: baseSpacing * 1,      // 4, 6, 8
    sm: baseSpacing * 2,      // 8, 12, 16
    md: baseSpacing * 3,      // 12, 18, 24
    lg: baseSpacing * 4,      // 16, 24, 32
    xl: baseSpacing * 6,      // 24, 36, 48
  };
};

/**
 * Hook for responsive font sizes
 */
export const useResponsiveFontSizes = (): ResponsiveFontSizes => {
  const { width, isSmallScreen, isTablet, fontScale } = useResponsiveDimensions();
  
  const baseFontSize = isSmallScreen ? 12 : isTablet ? 16 : 14;
  
  // Apply font scale for accessibility
  const scaledSize = (size: number) => Math.round(size * fontScale);
  
  return {
    xs: scaledSize(baseFontSize * 0.75),     // 9, 10.5, 12
    sm: scaledSize(baseFontSize),            // 12, 14, 16
    md: scaledSize(baseFontSize * 1.125),    // 13.5, 15.75, 18
    lg: scaledSize(baseFontSize * 1.25),     // 15, 17.5, 20
    xl: scaledSize(baseFontSize * 1.5),      // 18, 21, 24
    xxl: scaledSize(baseFontSize * 2),       // 24, 28, 32
  };
};

/**
 * Hook that combines responsive dimensions with safe area insets
 */
export const useResponsiveLayout = () => {
  const dimensions = useResponsiveDimensions();
  const spacing = useResponsiveSpacing();
  const fontSizes = useResponsiveFontSizes();
  const insets = useSafeAreaInsets();
  
  // Calculate content padding based on screen size
  const contentPadding = dimensions.isTablet 
    ? dimensions.width * 0.15 
    : dimensions.width * 0.05;
  
  // Calculate safe content height
  const safeContentHeight = dimensions.height - insets.top - insets.bottom;
  
  return {
    ...dimensions,
    spacing,
    fontSizes,
    insets,
    contentPadding,
    safeContentHeight,
  };
};

/**
 * Utility function to get responsive value based on screen size
 */
export const getResponsiveValue = <T>(
  values: {
    small?: T;
    medium?: T;
    large?: T;
    tablet?: T;
    default: T;
  },
  dimensions: ResponsiveDimensions
): T => {
  if (dimensions.isTablet && values.tablet !== undefined) {
    return values.tablet;
  }
  if (dimensions.isLargeScreen && values.large !== undefined) {
    return values.large;
  }
  if (dimensions.isMediumScreen && values.medium !== undefined) {
    return values.medium;
  }
  if (dimensions.isSmallScreen && values.small !== undefined) {
    return values.small;
  }
  return values.default;
};

/**
 * Utility function to scale values based on screen width
 */
export const scaleSize = (size: number, width: number, baseWidth: number = 375): number => {
  const scale = width / baseWidth;
  return Math.round(size * scale);
};

/**
 * Utility function to get minimum touch target size for accessibility
 */
export const getMinTouchTarget = (dimensions: ResponsiveDimensions): number => {
  return dimensions.isSmallScreen ? 44 : 48;
};
