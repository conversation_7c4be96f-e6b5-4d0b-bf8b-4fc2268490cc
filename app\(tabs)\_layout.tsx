import { Tabs } from 'expo-router';
import { Scan, Plus, Search, User } from 'lucide-react-native';
import { Dimensions } from 'react-native';
import AuthGuard from '../../components/AuthGuard';

const { width } = Dimensions.get('window');

export default function TabLayout() {
  return (
    <AuthGuard>
      <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#f97316',
        tabBarInactiveTintColor: '#6b7280',
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
          height: Math.max(90, width * 0.22),
          paddingBottom: Math.max(20, width * 0.05),
          paddingTop: Math.max(10, width * 0.025),
        },
        tabBarLabelStyle: {
          fontSize: Math.min(12, width * 0.03),
          fontFamily: 'Inter-Medium',
          marginTop: 4,
        },
        tabBarIconStyle: {
          marginTop: 4,
        },
      }}
    >
      <Tabs.Screen
        name="scan"
        options={{
          title: 'Scan',
          tabBarIcon: ({ size, color }) => (
            <Scan size={Math.min(size, width * 0.06)} color={color} strokeWidth={2} />
          ),
        }}
      />
      <Tabs.Screen
        name="add"
        options={{
          title: 'Add Product',
          tabBarIcon: ({ size, color }) => (
            <Plus size={Math.min(size, width * 0.06)} color={color} strokeWidth={2} />
          ),
        }}
      />
      <Tabs.Screen
        name="search"
        options={{
          title: 'Search',
          tabBarIcon: ({ size, color }) => (
            <Search size={Math.min(size, width * 0.06)} color={color} strokeWidth={2} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ size, color }) => (
            <User size={Math.min(size, width * 0.06)} color={color} strokeWidth={2} />
          ),
        }}
      />
      </Tabs>
    </AuthGuard>
  );
}